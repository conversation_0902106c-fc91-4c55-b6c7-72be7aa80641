import { Routes, Route } from "react-router";
import { Layout } from "./components/Layout";
import { ProtectedRoute } from "./components/ProtectedRoute";
import { BioAnalyzer } from "./pages/BioAnalyzer";
import { ImageAnalyzer } from "./pages/ImageAnalyzer";
import { LandingPage } from "./pages/LandingPage";
import { Dashboard } from "./pages/Dashboard";
import { AccountSettings } from "./pages/AccountSettings";

export function AppRoutes() {
  return (
    <Routes>
      <Route path="/" element={<Layout />}>
        <Route index element={<LandingPage />} />
        <Route
          path="dashboard"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="account-settings/*"
          element={
            <ProtectedRoute>
              <AccountSettings />
            </ProtectedRoute>
          }
        />
        <Route path="image-analyzer" element={<ImageAnalyzer />} />
        <Route path="bio-analyzer" element={<BioAnalyzer />} />
      </Route>
    </Routes>
  );
}
