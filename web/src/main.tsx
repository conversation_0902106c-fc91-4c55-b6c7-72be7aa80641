import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router";
import { ClerkProvider } from "@clerk/react-router";
import { PrivacyManager } from "./components/PrivacyManager";
import { AppRoutes } from "./routes";
import "./globals.css";

const publishableKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!publishableKey) {
  throw new Error("Missing Publishable Key");
}

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <BrowserRouter>
      <ClerkProvider publishableKey={publishableKey}>
        <PrivacyManager>
          <AppRoutes />
        </PrivacyManager>
      </ClerkProvider>
    </BrowserRouter>
  </StrictMode>
);
