/// <reference types="vite/client" />

import { openrouter } from "@openrouter/ai-sdk-provider";
import { generateText } from "ai";
import type { BioAnalysisProgress, BioAnalysisResult, StepResult } from "@/types/analysis";
import { BIO_ANALYSIS_STEPS } from "@/types/analysis";

export interface BioAnalysisCallbacks {
  onProgress?: (progress: BioAnalysisProgress) => void;
  onStepComplete?: (stepResult: StepResult) => void;
  onComplete?: (result: BioAnalysisResult) => void;
  onError?: (error: string) => void;
}

export class BioAnalysisAgent {
  private apiKey: string;

  constructor() {
    // Get API key from environment variables
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY;

    if (!this.apiKey) {
      console.error("🔑 VITE_OPENROUTER_API_KEY not found in environment variables");
      throw new Error(
        "OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file"
      );
    }

    console.log("🔑 OpenRouter API key loaded successfully for bio analysis");
    console.log(`🔑 API Key preview: ${this.apiKey.substring(0, 10)}...`);

    // Set the API key for OpenRouter provider
    if (typeof globalThis !== "undefined") {
      (globalThis as any).process = (globalThis as any).process || {};
      (globalThis as any).process.env = (globalThis as any).process.env || {};
      (globalThis as any).process.env.OPENROUTER_API_KEY = this.apiKey;
    }
  }

  private stepPrompts = {
    1: {
      name: "Writing Quality",
      system: "You are an expert writing coach specializing in dating profiles. Analyze the writing quality of the bio, including grammar, readability, flow, and overall structure.",
      prompt: (bio: string) =>
        `Analyze the writing quality of this dating bio and provide a score from 0-100 with specific insights:

Bio: "${bio}"

Focus on:
- Grammar and spelling accuracy
- Sentence structure and flow
- Clarity and conciseness
- Overall readability
- Word choice effectiveness

Provide your analysis in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Brief insight about specific writing aspect",
    "Another specific observation",
    "One more actionable point"
  ],
  "confidence": [number 0-100]
}`,
    },
    2: {
      name: "Personality Appeal",
      system: "You are a dating psychology expert. Analyze how well the bio conveys attractive personality traits and emotional appeal.",
      prompt: (bio: string) =>
        `Analyze the personality appeal of this dating bio and provide a score from 0-100:

Bio: "${bio}"

Evaluate:
- Authenticity and genuineness
- Humor and charm
- Confidence level
- Emotional intelligence
- Relatability factor

Provide your analysis in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Specific observation about personality traits shown",
    "Comment on emotional appeal or charm",
    "Suggestion for improvement"
  ],
  "confidence": [number 0-100]
}`,
    },
    3: {
      name: "Interest Analysis",
      system: "You are a dating coach specializing in lifestyle compatibility. Analyze how well the bio presents interesting and attractive lifestyle elements.",
      prompt: (bio: string) =>
        `Analyze the interest and lifestyle appeal of this dating bio:

Bio: "${bio}"

Assess:
- Variety and appeal of interests mentioned
- Lifestyle attractiveness
- Activity level and energy
- Conversation starter potential
- Uniqueness and memorability

Provide your analysis in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Observation about interests or hobbies mentioned",
    "Comment on lifestyle appeal",
    "Suggestion for adding engaging elements"
  ],
  "confidence": [number 0-100]
}`,
    },
    4: {
      name: "Dating Intent",
      system: "You are a relationship expert. Analyze how clearly the bio communicates dating intentions and relationship goals.",
      prompt: (bio: string) =>
        `Analyze the dating intent clarity of this bio:

Bio: "${bio}"

Evaluate:
- Clarity of relationship goals
- Serious vs casual dating signals
- Approach to commitment
- Emotional availability indicators
- Match compatibility signals

Provide your analysis in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Observation about dating goals clarity",
    "Comment on relationship signals sent",
    "Suggestion for better intent communication"
  ],
  "confidence": [number 0-100]
}`,
    },
    5: {
      name: "Engagement Factor",
      system: "You are a dating app optimization expert. Analyze how well the bio encourages matches and starts conversations.",
      prompt: (bio: string) =>
        `Analyze the engagement potential of this dating bio:

Bio: "${bio}"

Assess:
- Conversation starter elements
- Intrigue and curiosity factor
- Call-to-action effectiveness
- Approachability level
- Overall match appeal

Provide your analysis in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Specific element that encourages engagement",
    "Observation about conversation potential",
    "Suggestion for improving match appeal"
  ],
  "confidence": [number 0-100]
}`,
    },
  };

  async analyzeBio(
    bio: string,
    onProgress?: (stepId: number, stepName: string, progress: number) => void
  ): Promise<StepResult[]> {
    const results: StepResult[] = [];
    const totalSteps = BIO_ANALYSIS_STEPS.length;

    for (let i = 0; i < totalSteps; i++) {
      const step = BIO_ANALYSIS_STEPS[i];
      const stepPrompt = this.stepPrompts[step.id as keyof typeof this.stepPrompts];
      
      onProgress?.(step.id, step.name, (i / totalSteps) * 100);

      try {
        const startTime = Date.now();
        
        const { text } = await generateText({
          model: openrouter("openai/gpt-4o-mini"),
          system: stepPrompt.system,
          prompt: stepPrompt.prompt(bio),
        });

        const processingTime = Date.now() - startTime;
        
        // Parse the JSON response
        const analysisResult = JSON.parse(text);
        
        results.push({
          stepId: step.id,
          stepName: step.name,
          score: analysisResult.score,
          insights: analysisResult.insights,
          confidence: analysisResult.confidence,
          processingTime,
        });

        onProgress?.(step.id, step.name, ((i + 1) / totalSteps) * 100);
        
        // Small delay between steps to prevent overwhelming the API
        if (i < totalSteps - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error) {
        console.error(`Error in step ${step.id}:`, error);
        results.push({
          stepId: step.id,
          stepName: step.name,
          score: 0,
          insights: ["Analysis failed for this step"],
          confidence: 0,
          processingTime: 0,
        });
      }
    }

    return results;
  }

  calculateOverallScore(stepResults: StepResult[]): number {
    if (stepResults.length === 0) return 0;
    
    // Weighted average - some steps matter more for overall appeal
    const weights = {
      1: 0.15, // Writing Quality - 15%
      2: 0.30, // Personality Appeal - 30% (most important)
      3: 0.20, // Interest Analysis - 20%
      4: 0.15, // Dating Intent - 15%
      5: 0.20, // Engagement Factor - 20%
    };

    let weightedSum = 0;
    let totalWeight = 0;

    stepResults.forEach(result => {
      const weight = weights[result.stepId as keyof typeof weights] || 0.2;
      weightedSum += result.score * weight;
      totalWeight += weight;
    });

    return Math.round(weightedSum / totalWeight);
  }

  generateFinalRecommendations(stepResults: StepResult[]): string[] {
    const recommendations: string[] = [];
    
    // Find the lowest scoring areas and add targeted recommendations
    const sortedByScore = [...stepResults].sort((a, b) => a.score - b.score);
    
    sortedByScore.slice(0, 3).forEach(step => {
      if (step.score < 70) {
        recommendations.push(`Improve ${step.stepName.toLowerCase()}: ${step.insights[0]}`);
      }
    });

    // Add general recommendations based on overall pattern
    const averageScore = stepResults.reduce((sum, step) => sum + step.score, 0) / stepResults.length;
    
    if (averageScore < 50) {
      recommendations.push("Consider a complete bio rewrite focusing on your best qualities");
    } else if (averageScore < 70) {
      recommendations.push("Good foundation - polish specific areas for better impact");
    }

    // Ensure we have at least 3 recommendations
    while (recommendations.length < 3 && stepResults.length > 0) {
      const randomStep = stepResults[Math.floor(Math.random() * stepResults.length)];
      const insight = randomStep.insights[Math.floor(Math.random() * randomStep.insights.length)];
      if (!recommendations.some(rec => rec.includes(insight))) {
        recommendations.push(insight);
      }
    }

    return recommendations.slice(0, 5); // Max 5 recommendations
  }

  async generateImprovedBio(
    originalBio: string,
    stepResults: StepResult[],
    tone: "witty" | "sincere" | "adventurous" = "sincere"
  ): Promise<string> {
    const weaknesses = stepResults
      .filter(step => step.score < 70)
      .map(step => `${step.stepName}: ${step.insights.join(', ')}`)
      .join('\n');

    const { text } = await generateText({
      model: openrouter("openai/gpt-4o-mini"),
      system: `You are an expert dating profile writer. Create an improved bio that addresses the identified weaknesses while maintaining authenticity. The tone should be ${tone}.`,
      prompt: `Rewrite this dating bio to address these specific issues:

Original Bio: "${originalBio}"

Areas for improvement:
${weaknesses}

Requirements:
- Keep it under 150 words
- Maintain authenticity 
- Use a ${tone} tone
- Address the weaknesses identified
- Make it engaging and conversation-friendly
- Include specific details that make the person memorable

Provide only the improved bio text, no additional commentary.`,
    });

    return text.trim();
  }
}

export const bioAnalysisAgent = new BioAnalysisAgent();