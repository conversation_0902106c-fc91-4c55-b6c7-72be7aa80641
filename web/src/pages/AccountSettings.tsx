// import { UserProfile, useUser } from "@clerk/react-router";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Shield,
  Bell,
  CreditCard,
  Download,
  Trash2,
  AlertTriangle,
} from "lucide-react";
import { Link } from "react-router";

export function AccountSettings() {
  // const { user } = useUser();
  // Mock user data for now (replace with actual auth later)
  const user = {
    firstName: "Demo",
    lastName: "User",
    fullName: "Demo User",
    emailAddresses: [{ emailAddress: "<EMAIL>" }],
    primaryEmailAddress: { emailAddress: "<EMAIL>" },
    createdAt: new Date("2024-01-01").getTime()
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button asChild variant="ghost" size="sm">
            <Link to="/dashboard" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-slate-900">
              Account Settings
            </h1>
            <p className="text-slate-600 mt-1">
              Manage your account preferences and security settings
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Settings */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>
                  Update your personal information and profile details
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                {/* <UserProfile
                    appearance={{
                      elements: {
                        rootBox: "w-full max-w-full",
                        card: "shadow-none border-none rounded-none w-full max-w-full",
                        navbarMobileMenuButton: "hidden",
                      },
                    }}
                  /> */}
                <div className="space-y-4">
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">
                      User profile management temporarily disabled.
                    </p>
                    <p className="text-sm text-muted-foreground mt-2">
                      Authentication system is currently commented out for development.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Privacy & Security */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-green-600" />
                  Privacy & Security
                </CardTitle>
                <CardDescription>
                  Control your privacy settings and security preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Two-Factor Authentication</p>
                    <p className="text-sm text-muted-foreground">
                      Add an extra layer of security
                    </p>
                  </div>
                  <Badge variant="secondary">Enabled</Badge>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Data Privacy</p>
                    <p className="text-sm text-muted-foreground">
                      Control how your data is used
                    </p>
                  </div>
                  <Button variant="secondary" size="sm">
                    Manage
                  </Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Active Sessions</p>
                    <p className="text-sm text-muted-foreground">
                      See where you're logged in
                    </p>
                  </div>
                  <Button variant="secondary" size="sm">
                    View All
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Notifications */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5 text-blue-600" />
                  Notification Preferences
                </CardTitle>
                <CardDescription>
                  Choose what notifications you want to receive
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Email Notifications</p>
                    <p className="text-sm text-muted-foreground">
                      Get updates via email
                    </p>
                  </div>
                  <Badge>Enabled</Badge>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Match Alerts</p>
                    <p className="text-sm text-muted-foreground">
                      Notifications for new matches
                    </p>
                  </div>
                  <Badge>Enabled</Badge>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Analysis Completed</p>
                    <p className="text-sm text-muted-foreground">
                      When photo/bio analysis is done
                    </p>
                  </div>
                  <Badge variant="secondary">Disabled</Badge>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Weekly Reports</p>
                    <p className="text-sm text-muted-foreground">
                      Performance summaries
                    </p>
                  </div>
                  <Badge>Enabled</Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Account Status */}
            <Card>
              <CardHeader>
                <CardTitle>Account Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-flame-red to-sparks-pink rounded-full mx-auto mb-3 flex items-center justify-center">
                    <span className="text-white font-bold text-lg">
                      {user?.firstName?.[0] || "U"}
                    </span>
                  </div>
                  <p className="font-medium">{user?.fullName || "User"}</p>
                  <p className="text-sm text-muted-foreground">
                    {user?.primaryEmailAddress?.emailAddress}
                  </p>
                </div>
                <Separator />
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Account Type</span>
                    <Badge>Free</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Member Since</span>
                    <span>
                      {new Date(
                        user?.createdAt || Date.now()
                      ).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Subscription */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-purple-600" />
                  Subscription
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <Badge variant="secondary" className="mb-2">
                    Free Plan
                  </Badge>
                  <p className="text-sm text-muted-foreground mb-4">
                    Upgrade for unlimited analyses and premium features
                  </p>
                  <Button className="w-full bg-gradient-to-r from-flame-red to-sparks-pink hover:opacity-90">
                    Upgrade to Pro
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Data Management */}
            <Card>
              <CardHeader>
                <CardTitle>Data Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="secondary"
                  className="w-full justify-start"
                  size="sm"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </Button>
                <Button
                  variant="secondary"
                  className="w-full justify-start text-red-600 hover:text-red-700"
                  size="sm"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Account
                </Button>
              </CardContent>
            </Card>

            {/* Help & Support */}
            <Card>
              <CardHeader>
                <CardTitle>Need Help?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="secondary" className="w-full" size="sm">
                  Contact Support
                </Button>
                <Button variant="secondary" className="w-full" size="sm">
                  View Documentation
                </Button>
                <div className="flex items-start gap-2 p-3 bg-amber-50 rounded-lg">
                  <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="font-medium text-amber-800">Having issues?</p>
                    <p className="text-amber-700">
                      Check our FAQ or contact support for help.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
