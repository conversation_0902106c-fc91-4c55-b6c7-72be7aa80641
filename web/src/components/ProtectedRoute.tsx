// import { SignedIn, SignedOut, RedirectToSignIn } from "@clerk/react-router";
import { ReactNode } from "react";

interface ProtectedRouteProps {
  children: ReactNode;
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  // Temporarily bypass authentication - just render children directly
  return <>{children}</>;

  // Original Clerk authentication logic (commented out)
  // return (
  //   <>
  //     <SignedIn>
  //       {children}
  //     </SignedIn>
  //     <SignedOut>
  //       <RedirectToSignIn />
  //     </SignedOut>
  //   </>
  // );
}